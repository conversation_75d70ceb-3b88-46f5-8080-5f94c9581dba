<template>
    <Head title="Sign up">
        <meta name="description" content="Sign up to <PERSON>ra<PERSON>." />
    </Head>

    <AuthLayout>
        <div class="w-full max-w-[60%] md:px-24 lg:px-48">
            <div class="text-center">
                <h1 class="fs-2xl mb-8 font-bold">Create an account</h1>
                <p>Start your 28 day trial now!</p>
            </div>

            <form @submit.prevent="submit" class="my-40">

                <TextInput
                    id="first_name"
                    name="first_name"
                    v-model="form.first_name"
                    label="First name"
                    type="text"
                    required
                    :rules="[rules.required]"
                    :serverError="form.errors.first_name"
                    :paddingWithoutText="customPadding"
                ></TextInput>

                <TextInput
                    id="last_name"
                    v-model="form.last_name"
                    name="last_name"
                    label="Last name"
                    type="text"
                    required
                    :rules="[rules.required]"
                    :serverError="form.errors.last_name"
                    :paddingWithoutText="customPadding"
                ></TextInput>

                <TextInput
                    id="email"
                    v-model="form.email"
                    name="email"
                    label="Email address"
                    type="email"
                    required
                    :rules="[rules.required, rules.email]"
                    :serverError="form.errors.email"
                    :paddingWithoutText="customPadding"
                ></TextInput>

                <TextInput
                    id="password"
                    v-model="form.password"
                    name="password"
                    label="Password"
                    type="password"
                    required
                    :rules="[rules.required, rules.password]"
                    :serverError="form.errors.password"
                    :paddingWithoutText="customPadding"
                ></TextInput>

                <div class="min-h-[88px]">
                    <ul v-show="password" class="fs-xs mb-32 space-y-4">
                        <li class="flex items-center gap-8">
                            <component
                                :is="
                                    isMinLength ? IconCircleCheck : IconCircleX
                                "
                            />
                            At least 8 characters
                        </li>
                        <li class="flex items-center gap-8">
                            <component
                                :is="hasNumber ? IconCircleCheck : IconCircleX"
                            />
                            At least one number
                        </li>
                        <li class="flex items-center gap-8">
                            <component
                                :is="
                                    hasUpperAndLower
                                        ? IconCircleCheck
                                        : IconCircleX
                                "
                            />
                            Upper and lower case
                        </li>
                    </ul>
                </div>

                <Checkbox
                    class="fs-xs mt-20"
                    id="marketing_checkbox"
                    v-model:checked="form.marketing"
                    name="marketing_checkbox"
                    required
                    :errorMessage="form.errors.marketing"
                >
                    Yes, I want to receive updates and special offers from Pravi
                </Checkbox>

                <Checkbox
                    v-if="$page.props.jetstream.hasTermsAndPrivacyPolicyFeature"
                    id="terms_checkbox"
                    v-model:checked="form.terms"
                    name="terms_checkbox"
                    required
                    :errorMessage="form.errors.terms"
                    class="fs-xs"
                >
                    I accept the
                    <a
                        target="_blank"
                        :href="route('terms.show')"
                        class="text-surface-action underline"
                        >terms and conditions
                    </a>
                    and
                    <a
                        target="_blank"
                        :href="route('policy.show')"
                        class="text-surface-action underline"
                        >privacy policy
                    </a>
                </Checkbox>

                <Button
                    type="submit"
                    class="h-[48px] w-full"
                    color="action"
                    size="md"
                    :disabled="form.processing"
                >
                    Sign Up
                </Button>
            </form>

            <div
                class="mt-40 w-full pt-40 text-center"
            >
                <p>
                    Already have an account?

                    <Link
                        :href="route('login')"
                        class="text-surface-action underline"
                    >
                        Log in
                    </Link>
                </p>
            </div>
        </div>
    </AuthLayout>
</template>

<script setup>
import { Link, useForm, Head } from "@inertiajs/vue3";
import TextInput from "@/Components/TextInput/TextInput.vue";
import Button from "@/Components/Button/Button.vue";
import Checkbox from "@/Components/Checkbox/Checkbox.vue";

import AuthLayout from "@/Layouts/AuthLayout.vue";
import rules from "@/utilities/validation-rules";
import IconCircleCheck from "@/Components/Icons/IconCircleCheck.vue";
import IconCircleX from "@/Components/Icons/IconCircleX.vue";
import { computed } from "vue";

const customPadding = "pb-28 md:pb-12";

const form = useForm({
    first_name: "",
    last_name: "",
    email: "",
    password: "",

    marketing: false,
    terms: false,
});
const password = computed(() => form.password);

const isMinLength = computed(() => password.value.length >= 8);
const hasNumber = computed(() => /\d/.test(password.value));
const hasUpperAndLower = computed(
    () => /[a-z]/.test(password.value) && /[A-Z]/.test(password.value),
);

const urlParams = new URLSearchParams(window.location.search);
const urlEmail = urlParams.get('email');
const urlFirstName = urlParams.get('first_name');
const urlLastName = urlParams.get('last_name');

if (urlEmail && !form.email) {
    form.email = urlEmail;
}

if (urlFirstName && !form.first_name) {
    form.first_name = urlFirstName;
}

if (urlLastName && !form.last_name) {
    form.last_name = urlLastName;
}


const submit = () => {
    form.post(route("api.register"), {
        onFinish: () => {
            form.reset("password");
        },
    });
};
</script>
