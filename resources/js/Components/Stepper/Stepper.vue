<template>
    <div class="w-full">
        <!-- Progress bar container -->
        <div class="relative h-12 w-full rounded-full bg-surface-grey-light overflow-hidden">
            <!-- Progress fill -->
            <div
                class="absolute left-0 top-0 h-12 rounded-full bg-surface-action progress-fill"
                :class="{
                    'progress-complete': isComplete,
                    'progress-animating': isAnimating
                }"
                :style="{ width: overallProgressPercentage + '%' }"
                @transitionstart="handleTransitionStart"
                @transitionend="handleTransitionEnd"
            ></div>

            <!-- Completion glow effect -->
            <div
                v-if="isComplete"
                class="absolute inset-0 rounded-full bg-surface-action opacity-30 animate-pulse"
            ></div>
        </div>

        <!-- Progress text (optional) -->
        <!-- <div class="mt-12 text-center text-sm text-text-body">
            Step {{ currentStep.step }} of {{ steps.length }}
        </div> -->
    </div>
</template>

<script>
export default {
    name: "Stepper",
    props: {
        /**
         * Array of sub-step counts per main step
         */
        steps: {
            type: Array,
            required: true,
            validator(v) {
                return (
                    Array.isArray(v) &&
                    v.every((n) => typeof n === "number" && n > 0)
                );
            },
        },
        /**
         * Current position: which main step and which sub-step within it
         */
        currentStep: {
            type: Object,
            required: true,
            validator(o) {
                // Only basic shape validation here, cross-props checks done in usage
                return (
                    o && typeof o.step === "number" && typeof o.sub === "number"
                );
            },
        },
    },
    data() {
        return {
            isAnimating: false,
        };
    },

    computed: {
        /**
         * Calculate overall progress percentage across all steps and sub-steps
         */
        overallProgressPercentage() {
            const { step, sub } = this.currentStep;
            const totalSteps = this.steps.length;

            // Clamp current step to valid range
            const currentMainStep = Math.min(Math.max(step, 1), totalSteps);

            // Calculate total sub-steps across all main steps
            const totalSubSteps = this.steps.reduce((sum, stepCount) => sum + stepCount, 0);

            // Calculate completed sub-steps
            let completedSubSteps = 0;

            // Add all sub-steps from completed main steps
            for (let i = 0; i < currentMainStep - 1; i++) {
                completedSubSteps += this.steps[i];
            }

            // Add current sub-steps from current main step
            if (currentMainStep <= totalSteps) {
                const currentStepSubSteps = this.steps[currentMainStep - 1];
                const currentSub = Math.min(Math.max(sub, 1), currentStepSubSteps);
                completedSubSteps += currentSub;
            }

            // Calculate percentage (ensure we don't exceed 100%)
            const percentage = Math.min((completedSubSteps / totalSubSteps) * 100, 100);
            return Math.round(percentage);
        },

        /**
         * Check if progress is complete (100%)
         */
        isComplete() {
            return this.overallProgressPercentage >= 100;
        }
    },

    methods: {
        /**
         * Percent fill of segment after main step i (kept for backward compatibility)
         */
        segmentFillWidth(index) {
            const { step, sub } = this.currentStep;
            const totalSteps = this.steps.length;
            const s = Math.min(Math.max(step, 1), totalSteps); // Clamp step
            const maxSub = this.steps[index - 1] || 1; // How many substeps at this segment

            if (s > index) {
                // If we're past this segment, it's fully filled
                return 100;
            }

            if (s < index) {
                // If we haven't reached this segment yet, it's empty
                return 0;
            }

            // We are *at* this segment:
            if (maxSub === 1) {
                // If this step has only 1 substep, don't fill it partially — keep at 0
                return 0;
            }

            const sb = Math.min(Math.max(sub, 1), maxSub);
            return Math.round((sb / maxSub) * 100) - 25;
        },

        isStepReached(index) {
            const { step } = this.currentStep;
            if (index <= step) return true;
        },

        /**
         * Handle transition start event
         */
        handleTransitionStart() {
            this.isAnimating = true;
        },

        /**
         * Handle transition end event
         */
        handleTransitionEnd() {
            this.isAnimating = false;
        },
    },
};
</script>

<style scoped>
/* Enhanced progress bar animations */
.progress-fill {
    transition: width 600ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateZ(0); /* Force hardware acceleration */
    will-change: width;
}

/* Faster animation when actively changing */
.progress-animating {
    transition: width 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Completion state with enhanced visual feedback */
.progress-complete {
    transition: width 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 0 20px rgba(241, 57, 151, 0.4);
}

/* Completion celebration effect */
.progress-complete::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(241, 57, 151, 0.6),
        rgba(241, 57, 151, 0.3),
        rgba(241, 57, 151, 0.6)
    );
    border-radius: inherit;
    z-index: -1;
    animation: completionGlow 2s ease-in-out infinite;
}

/* Keyframe animation for completion glow */
@keyframes completionGlow {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.02);
    }
}

/* Smooth hover effect for better interactivity */
.progress-fill:hover {
    filter: brightness(1.1);
    transition: filter 200ms ease-in-out, width 600ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .progress-fill {
        transition: width 200ms ease-out;
    }

    .progress-complete::after {
        animation: none;
    }

    .animate-pulse {
        animation: none;
    }
}
</style>
