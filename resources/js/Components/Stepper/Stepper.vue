<template>
    <div class="w-full">
        <!-- Progress bar container -->
        <div class="relative h-12 w-full rounded-full bg-surface-grey-light">
            <!-- Progress fill -->
            <div
                class="absolute left-0 top-0 h-12 rounded-full bg-surface-action transition-all duration-300 ease-in-out"
                :style="{ width: overallProgressPercentage + '%' }"
            ></div>
        </div>

        <!-- Progress text (optional) -->
        <div class="mt-12 text-center text-sm text-text-body">
            Step {{ currentStep.step }} of {{ steps.length }}
        </div>
    </div>
</template>

<script>
export default {
    name: "Stepper",
    props: {
        /**
         * Array of sub-step counts per main step
         */
        steps: {
            type: Array,
            required: true,
            validator(v) {
                return (
                    Array.isArray(v) &&
                    v.every((n) => typeof n === "number" && n > 0)
                );
            },
        },
        /**
         * Current position: which main step and which sub-step within it
         */
        currentStep: {
            type: Object,
            required: true,
            validator(o) {
                // Only basic shape validation here, cross-props checks done in usage
                return (
                    o && typeof o.step === "number" && typeof o.sub === "number"
                );
            },
        },
    },
    computed: {
        /**
         * Calculate overall progress percentage across all steps and sub-steps
         */
        overallProgressPercentage() {
            const { step, sub } = this.currentStep;
            const totalSteps = this.steps.length;

            // Clamp current step to valid range
            const currentMainStep = Math.min(Math.max(step, 1), totalSteps);

            // Calculate total sub-steps across all main steps
            const totalSubSteps = this.steps.reduce((sum, stepCount) => sum + stepCount, 0);

            // Calculate completed sub-steps
            let completedSubSteps = 0;

            // Add all sub-steps from completed main steps
            for (let i = 0; i < currentMainStep - 1; i++) {
                completedSubSteps += this.steps[i];
            }

            // Add current sub-steps from current main step
            if (currentMainStep <= totalSteps) {
                const currentStepSubSteps = this.steps[currentMainStep - 1];
                const currentSub = Math.min(Math.max(sub, 1), currentStepSubSteps);
                completedSubSteps += currentSub;
            }

            // Calculate percentage (ensure we don't exceed 100%)
            const percentage = Math.min((completedSubSteps / totalSubSteps) * 100, 100);
            return Math.round(percentage);
        }
    },

    methods: {
        /**
         * Percent fill of segment after main step i (kept for backward compatibility)
         */
        segmentFillWidth(index) {
            const { step, sub } = this.currentStep;
            const totalSteps = this.steps.length;
            const s = Math.min(Math.max(step, 1), totalSteps); // Clamp step
            const maxSub = this.steps[index - 1] || 1; // How many substeps at this segment

            if (s > index) {
                // If we're past this segment, it's fully filled
                return 100;
            }

            if (s < index) {
                // If we haven't reached this segment yet, it's empty
                return 0;
            }

            // We are *at* this segment:
            if (maxSub === 1) {
                // If this step has only 1 substep, don't fill it partially — keep at 0
                return 0;
            }

            const sb = Math.min(Math.max(sub, 1), maxSub);
            return Math.round((sb / maxSub) * 100) - 25;
        },

        isStepReached(index) {
            const { step } = this.currentStep;
            if (index <= step) return true;
        },
    },
};
</script>
