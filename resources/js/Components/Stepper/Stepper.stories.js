import Stepper from "./Stepper.vue";

export default {
    component: Stepper,
    tags: ["autodocs"],
    argTypes: {
        steps: {
            control: "object",
            description: "Array of sub-step counts per main step"
        },
        currentStep: {
            control: "object",
            description: "Current position: which main step and which sub-step within it"
        },
    },
};

export const Default = {
    args: {
        // 4 main steps: first has 4 sub-steps, the rest have 1 each
        steps: [4, 1, 1, 1],
        // starting at step 2, sub-step 1 (71% progress)
        currentStep: { step: 2, sub: 1 },
    },
};

export const Beginning = {
    args: {
        steps: [4, 1, 1, 1],
        // At the very beginning (14% progress)
        currentStep: { step: 1, sub: 1 },
    },
};

export const MiddleOfFirstStep = {
    args: {
        steps: [4, 1, 1, 1],
        // Halfway through first step (43% progress)
        currentStep: { step: 1, sub: 3 },
    },
};

export const NearCompletion = {
    args: {
        steps: [4, 1, 1, 1],
        // Almost done (86% progress)
        currentStep: { step: 3, sub: 1 },
    },
};

export const Completed = {
    args: {
        steps: [4, 1, 1, 1],
        // Fully completed (100% progress)
        currentStep: { step: 4, sub: 1 },
    },
};

export const SimpleSteps = {
    args: {
        // Simple case: 3 steps with 1 sub-step each
        steps: [1, 1, 1],
        // At step 2 (67% progress)
        currentStep: { step: 2, sub: 1 },
    },
};
